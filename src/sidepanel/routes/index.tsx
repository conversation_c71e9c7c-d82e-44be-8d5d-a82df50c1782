import { useEffect } from 'react'
import { Route, Routes, useNavigate } from 'react-router-dom'
import { useToolType } from '@src/common/hooks'
import { routesConfig } from './config'

interface RoutingProps {
  selectedText?: string;
  textOperation?: string;
}

export default ({ selectedText, textOperation }: RoutingProps) => {
  const navigation = useNavigate()
  const { isTools } = useToolType()

  useEffect(() => {
    // 插件默认初始路由是/，如果是工具tab需要跳转到工具页面
    if (isTools) {
      navigation(routesConfig.tools.path)
    }
  }, [])

  return (
    <Routes>
      {Object.values(routesConfig).map(({ path, Component }) => {
        // 将选中的文本和操作类型传递给页面组件
        return (
          <Route
            key={path}
            path={path}
            element={
              <Component
                selectedText={selectedText}
                textOperation={textOperation}
              />
            }
          />
        )
      })}
    </Routes>
  )
}
