import { useNavigate } from 'react-router-dom'
import { Tooltip, Segmented } from 'antd/es'
import { type NavbarProps } from '@ht/chatui'
import { log } from '@ht/xlog'
import {
  useAppDispatch,
  handleTabType,
  TAB_TYPE_CONFIG,
  type TtabType,
} from '@src/store'
import { pcHistory } from '@src/common/images'
import { routesConfig } from '@src/sidepanel/routes/config'
import { SettingIcon } from '@src/common/Icons'
import './style.less'

interface INavbarProps extends NavbarProps {
  hideHistory?: boolean
}

export default (props: INavbarProps) => {
  const { onHistoryButtonClick, hideHistory } = props
  const dispatch = useAppDispatch()
  const navigation = useNavigate()
  const { tabType } = useToolType()

  const reportLog = (pageTitle) => {
    log({
      id: 'button_click',
      page_id: 'navbar',
      page_title: pageTitle,
    })
  }

  const handleHistoryButtonClick = () => {
    reportLog('查看历史会话')
    onHistoryButtonClick()
  }

  const handleSettingsClick = async () => {
    reportLog('打开配置页面')
    try {
      // 在新标签页中打开配置页面
      // 使用与upgrade页面相同的路径格式
      const settingsUrl = `chrome-extension://${chrome.runtime.id}/tabs/settings.html`

      console.log('Opening settings URL:', settingsUrl)
      await chrome.tabs.create({
        url: settingsUrl,
      })
    } catch (error) {
      console.error('Failed to open settings page:', error)
      // 如果打开新标签页失败，则显示错误信息
      console.error('Settings page navigation failed')
    }
  }

  const handleSegmentChange = (value: TtabType) => {
    reportLog(value)
    dispatch(handleTabType(value))
    if (value === TAB_TYPE_CONFIG.CHAR.value) {
      navigation(routesConfig.home.path)
    } else if (value === TAB_TYPE_CONFIG.TOOLS.value) {
      navigation(routesConfig.tools.path)
    }
  }

  return (
    <div className="htsc-chatui-navbar">
      <Segmented
        options={Object.keys(TAB_TYPE_CONFIG).map((key) => {
          const { value, Icon } = TAB_TYPE_CONFIG[key]
          return { label: value, value, icon: <Icon /> }
        })}
        value={tabType}
        onChange={handleSegmentChange}
      />

      {hideHistory ? null : (
        <Tooltip
          title="查看历史会话"
          placement="bottomRight"
          className="history-icon"
        >
          <img src={pcHistory} onClick={handleHistoryButtonClick} />
        </Tooltip>
      )}
      <Tooltip title="配置" placement="bottomRight" className="settings-icon">
        <div className="settings-icon-wrapper" onClick={handleSettingsClick}>
          <SettingIcon />
        </div>
      </Tooltip>
    </div>
  )
}
