import React, { useEffect, useRef } from 'react'
import { Button, message } from 'antd/es'
import { log } from '@ht/xlog'
import { useAppDispatch, handleModelType } from '@src/store'
import { MessageType } from '@src/common/const'
import { MessageIcon, PageSummaryIcon, TranslateIcon } from '@src/common/Icons'
import { getCurrentTab } from '@src/sidepanel/utils'
import './style.less'

export default React.forwardRef((props: any, ref) => {
  const { chatUiRef } = props
  const dispatch = useAppDispatch()
  const [messageApi, contextHolder] = message.useMessage()
  const { modelType, modelTypeLabel, isPagesummary } = useAiChat()
  const didMountRef = useRef(false)
  const { general, pagesummary, translate } = ModelType

  const chatButtons = [
    {
      name: general.label,
      icon: <MessageIcon />,
      onClick: () => handleNewChat(general.label),
    },
    {
      name: pagesummary.label,
      icon: <PageSummaryIcon />,
      onClick: () => handlePageSummary(pagesummary.label),
    },
    {
      name: translate.label,
      icon: <TranslateIcon />,
      onClick: () => handleTranslate(translate.label),
    },
  ]

  const reportLog = (pageTitle) => {
    log({
      id: 'button_click',
      page_id: 'quickReplyBtn',
      page_title: pageTitle,
    })
  }

  // 检查content script是否已注入
  const checkContentScriptInjected = async (
    tabId: number
  ): Promise<boolean> => {
    try {
      return await new Promise((resolve) => {
        chrome.tabs.sendMessage(
          tabId,
          { type: MessageType.CHECK_CONTENT_SCRIPT },
          (response) => {
            if (chrome.runtime.lastError) {
              resolve(false)
            } else {
              resolve(true)
            }
          }
        )
      })
    } catch (error) {
      console.error('检查content script注入失败:', error)
      return false
    }
  }

  // 刷新当前页面
  const refreshCurrentPage = async (tabId: number) => {
    try {
      await chrome.tabs.reload(tabId)
    } catch (error) { }
  }

  // 向当前标签页发送消息，启动翻译功能
  async function handleTranslate(pageTitle) {
    const { handleNewConversation } = chatUiRef?.current?.chatContext || {}

    const handleStartTranslate = (currentTab) => {
      reportLog(pageTitle)
      dispatch(handleModelType(ModelType.translate.value))
      handleNewConversation?.()
      chrome.tabs.sendMessage(currentTab.id, {
        type: MessageType.START_TRANSLATE,
      })
    }

    const currentTab = await getCurrentTab(messageApi)
    const isInjected = await checkContentScriptInjected(currentTab.id)
    if (!isInjected) {
      // 如果未注入，刷新页面
      await refreshCurrentPage(currentTab.id)
      // 监听页面加载完成事件
      chrome.tabs.onUpdated.addListener(
        async function listener(tabId, changeInfo) {
          if (tabId === currentTab.id && changeInfo.status === 'complete') {
            // 页面加载完成后，移除监听器
            chrome.tabs.onUpdated.removeListener(listener)

            // 确保content script注入完成
            const isInjectedAfterReload = await checkContentScriptInjected(
              currentTab.id
            )
            if (isInjectedAfterReload) {
              // content script已注入，继续执行翻译
              handleStartTranslate(currentTab)
            } else {
              messageApi.error('翻译功能注入失败，请手动刷新页面后重试')
            }
          }
        }
      )
      return
    }
    handleStartTranslate(currentTab)
  }

  // 新建会话
  function handleNewChat(pageTitle) {
    const { handleNewConversation } = chatUiRef?.current?.chatContext || {}
    reportLog(pageTitle)
    dispatch(handleModelType(ModelType.general.value))
    handleNewConversation?.()
  }

  // 网页问答
  async function handlePageSummary(pageTitle) {
    if (isPagesummary) {
      messageApi.info('请直接输入您的问题')
      return
    }
    reportLog(pageTitle)
    dispatch(handleModelType(ModelType.pagesummary.value))
  }

  const handleGenerateSummary = async () => {
    const { handleNewConversation } = chatUiRef?.current?.chatContext || {}
    await getCurrentTab(messageApi)
    handleNewConversation?.()
  }

  useEffect(() => {
    if (!isPagesummary) {
      return
    }
    // 网页总结模型
    if (didMountRef.current) {
      handleGenerateSummary() // 只在modelType被设置为pagesummary时触发
    } else {
      didMountRef.current = true // 首次渲染时不触发
    }
  }, [modelType])
  return (
    <div className="chat-above-node">
      {contextHolder}
      {chatButtons.map((chatButtonItem) => {
        const { icon, onClick, name } = chatButtonItem
        return (
          <Button
            key={name}
            className={`chat-button ${name === modelTypeLabel ? 'active' : ''} ${name === general.label ? 'general' : ''}`}
            icon={icon}
            onClick={onClick}
          >
            {name}
          </Button>
        )
      })}
    </div>
  )
})
